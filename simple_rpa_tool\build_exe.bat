@echo off
chcp 65001 >nul
echo ========================================
echo 小白专用RPA工具 - 打包脚本
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 检查虚拟环境
if not exist "venv_rpa311" (
    echo ❌ 虚拟环境不存在
    echo 请先运行 install_and_run.bat 创建虚拟环境
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv_rpa311\Scripts\activate.bat

REM 检查Python版本和路径
echo.
echo 📍 当前Python信息:
python --version
echo Python路径: 
where python
echo.

REM 检查主程序文件
if not exist "main_gui.py" (
    echo ❌ 主程序文件 main_gui.py 不存在
    pause
    exit /b 1
)

REM 安装/升级打包工具
echo 📦 安装打包工具...
pip install --upgrade pyinstaller

REM 清理旧的构建文件
echo 🧹 清理旧文件...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build
if exist "*.spec" del *.spec

REM 创建图标文件（如果不存在）
if not exist "icon.ico" (
    echo 🎨 创建默认图标...
    echo. > icon.ico
)

REM 开始打包
echo.
echo 🚀 开始打包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM PyInstaller打包命令
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="小白专用RPA工具" ^
    --icon=icon.ico ^
    --add-data="src;src" ^
    --add-data="config;config" ^
    --add-data="templates;templates" ^
    --hidden-import=selenium ^
    --hidden-import=webdriver_manager ^
    --hidden-import=pandas ^
    --hidden-import=openpyxl ^
    --hidden-import=requests ^
    --hidden-import=tkinter ^
    --hidden-import=customtkinter ^
    --collect-all=selenium ^
    --collect-all=webdriver_manager ^
    --distpath=dist ^
    --workpath=build ^
    --specpath=. ^
    main_gui.py

REM 检查打包结果
echo.
if exist "dist\小白专用RPA工具.exe" (
    echo ✅ 打包成功！
    echo.
    echo 📁 输出文件: dist\小白专用RPA工具.exe
    echo 📊 文件大小: 
    dir "dist\小白专用RPA工具.exe" | findstr "小白专用RPA工具.exe"
    echo.
    
    REM 复制必要文件到dist目录
    echo 📋 复制配置文件...
    if exist "config" xcopy /E /I /Y config dist\config\
    if exist "templates" xcopy /E /I /Y templates dist\templates\
    if exist "README.md" copy README.md dist\
    
    echo.
    echo 🎉 打包完成！
    echo.
    echo 📝 使用说明:
    echo 1. exe文件位于 dist 目录
    echo 2. 可以将整个 dist 目录复制到其他电脑
    echo 3. 双击 小白专用RPA工具.exe 即可运行
    echo 4. 首次运行会自动创建必要的目录
    echo.
    
    REM 询问是否测试运行
    echo 🧪 是否现在测试运行打包的exe文件? (Y/N)
    set /p test_choice=
    if /i "%test_choice%"=="Y" (
        echo.
        echo 🚀 启动测试...
        start "" "dist\小白专用RPA工具.exe"
    )
    
) else (
    echo ❌ 打包失败！
    echo.
    echo 🔍 可能的原因:
    echo 1. 依赖包未正确安装
    echo 2. Python版本不兼容
    echo 3. 虚拟环境配置问题
    echo 4. 磁盘空间不足
    echo.
    echo 📋 解决方法:
    echo 1. 检查上面的错误信息
    echo 2. 确保所有依赖包已安装
    echo 3. 尝试重新创建虚拟环境
    echo 4. 查看 build 目录下的日志文件
    echo.
)

REM 显示详细信息
echo.
echo ========================================
echo 打包信息
echo ========================================
echo 项目目录: %CD%
echo Python版本: 
python --version
echo PyInstaller版本:
pyinstaller --version
echo 虚拟环境: venv_rpa311
echo 打包时间: %date% %time%
echo ========================================

echo.
echo 按任意键退出...
pause >nul

REM 停用虚拟环境
if defined VIRTUAL_ENV (
    deactivate
)
