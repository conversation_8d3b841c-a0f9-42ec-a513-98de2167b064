# RPA工具依赖包 - 适用于Python 3.11.6
# 核心依赖
selenium==4.18.1
webdriver-manager==4.0.1
requests==2.31.0
pandas==2.2.1
openpyxl==3.1.2
PyYAML==6.0.1

# GUI界面
tkinter-tooltip==2.1.0
pillow==10.2.0
customtkinter==5.2.2

# 数据处理
numpy==1.26.4
lxml==5.1.0
beautifulsoup4==4.12.3

# 网络和安全
urllib3==2.2.1
certifi==2024.2.2

# 文件处理和工具
pathlib2==2.3.7
python-dotenv==1.0.1

# 日志
colorlog==6.8.2

# 定时任务
schedule==1.2.1
APScheduler==3.10.4

# 打包工具
pyinstaller==6.5.0

# 系统集成
pywin32==306
psutil==5.9.8

# 聊天软件集成（可选）
# wechatpy==1.8.18
# requests-toolbelt==1.0.0

# 开发工具（可选）
# pytest==8.1.1
# black==24.3.0
