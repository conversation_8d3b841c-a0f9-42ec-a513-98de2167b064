# 小白专用RPA工具 - 用户指南

## 📖 目录

1. [快速开始](#快速开始)
2. [界面介绍](#界面介绍)
3. [基本配置](#基本配置)
4. [任务执行](#任务执行)
5. [定时任务](#定时任务)
6. [高级功能](#高级功能)
7. [常见问题](#常见问题)

## 🚀 快速开始

### 第一次使用

1. **安装Python 3.11.6**
   - 双击运行 `setup_python311.bat`
   - 等待自动下载和安装完成

2. **安装RPA工具**
   - 双击运行 `install_and_run.bat`
   - 等待虚拟环境创建和依赖安装

3. **启动工具**
   - 双击运行 `main_gui.py`
   - 或使用打包后的exe文件

### 基本使用流程

```
配置网站信息 → 设置输出选项 → 执行任务 → 查看结果
```

## 🖥️ 界面介绍

### 主界面布局

```
┌─────────────────────────────────────┐
│           🤖 小白专用RPA工具          │
├─────────────────────────────────────┤
│ ⚙️基本配置 │ 🚀任务执行 │ ⏰定时任务 │ 📋日志 │
├─────────────────────────────────────┤
│                                     │
│            选项卡内容区域             │
│                                     │
├─────────────────────────────────────┤
│ 💾保存配置 │ 📁打开目录 │ 📖帮助 │ ❌退出 │
└─────────────────────────────────────┘
```

### 选项卡功能

- **⚙️ 基本配置**: 设置目标网站、登录信息、浏览器选项
- **🚀 任务执行**: 执行RPA任务、查看进度和结果
- **⏰ 定时任务**: 设置自动执行时间和频率
- **📋 日志**: 查看详细的执行日志和错误信息

## ⚙️ 基本配置

### 网站配置

1. **目标网站URL**
   - 输入要自动化的网站地址
   - 例如: `https://example.com/login`

2. **登录信息**
   - 用户名: 网站登录用户名
   - 密码: 网站登录密码
   - 注意: 密码会以星号显示，确保安全

### 浏览器配置

1. **浏览器类型**
   - Chrome (推荐): 兼容性最好
   - Firefox: 备选方案
   - Edge: Windows系统推荐

2. **运行模式**
   - 正常模式: 显示浏览器窗口，便于调试
   - 后台模式: 隐藏浏览器窗口，提高效率

### 输出配置

1. **输出目录**
   - 默认: `output` 文件夹
   - 可自定义任意目录路径

2. **文件格式**
   - Excel (.xlsx): 推荐，支持格式化
   - CSV (.csv): 通用格式，兼容性好
   - JSON (.json): 程序员友好格式

## 🚀 任务执行

### 执行步骤

1. **开始执行**
   - 点击 "▶️ 开始执行" 按钮
   - 观察进度条和状态信息

2. **执行过程**
   ```
   🔧 初始化浏览器 → 🔑 登录网站 → 📊 提取数据 → 💾 保存文件
   ```

3. **查看结果**
   - 在结果区域查看执行日志
   - 检查输出目录中的文件

### 测试功能

- **🧪 测试连接**: 验证网站是否可访问
- **⏹️ 停止任务**: 中断正在执行的任务

### 结果文件

生成的文件命名格式:
```
rpa_data_20240101_143022.xlsx
```
- `rpa_data`: 固定前缀
- `20240101`: 日期 (年月日)
- `143022`: 时间 (时分秒)
- `.xlsx`: 文件扩展名

## ⏰ 定时任务

### 设置定时执行

1. **启用定时任务**
   - 勾选 "启用定时任务" 复选框

2. **设置执行时间**
   - 小时: 0-23
   - 分钟: 0-59
   - 例如: 09:30 表示上午9点30分

3. **选择执行频率**
   - 每天: 每天执行一次
   - 工作日: 周一到周五执行
   - 自定义: 自定义执行规则

### 启动定时任务

- 点击 "启动定时任务" 按钮
- 程序将在后台等待执行时间
- 可以最小化窗口，不影响定时执行

## 🔧 高级功能

### 命令行模式

适合高级用户和批处理:

```bash
# 执行单次任务
python main_cli.py --run

# 使用指定配置
python main_cli.py --run --config my_task.json

# 测试连接
python main_cli.py --test --url https://example.com

# 验证配置
python main_cli.py --validate
```

### 配置文件

手动编辑 `config/user_config.json`:

```json
{
  "url": "https://example.com/login",
  "username": "your_username",
  "password": "your_password",
  "browser": "chrome",
  "headless": false,
  "output_dir": "output",
  "format": "Excel (.xlsx)"
}
```

### 批量处理

1. 创建多个配置文件
2. 使用命令行依次执行
3. 或编写批处理脚本自动化

## ❓ 常见问题

### 安装问题

**Q: Python安装失败怎么办？**
A: 
1. 以管理员身份运行安装脚本
2. 检查网络连接
3. 手动下载Python安装包

**Q: 依赖包安装失败？**
A:
1. 使用国内镜像源: `pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`
2. 检查网络连接
3. 尝试重新创建虚拟环境

### 使用问题

**Q: 无法找到登录框？**
A:
1. 检查网站URL是否正确
2. 确认网站结构没有变化
3. 尝试手动登录验证

**Q: 数据提取为空？**
A:
1. 检查网站是否需要登录
2. 确认数据加载完成
3. 查看日志了解详细错误

**Q: exe文件无法运行？**
A:
1. 检查杀毒软件是否拦截
2. 确保所有依赖文件完整
3. 以管理员身份运行

### 性能问题

**Q: 执行速度慢？**
A:
1. 启用无头模式
2. 关闭不必要的浏览器功能
3. 优化网络连接

**Q: 内存占用高？**
A:
1. 及时关闭浏览器
2. 清理临时文件
3. 重启程序

## 📞 技术支持

### 获取帮助

1. **查看日志**: `logs/` 目录下的日志文件
2. **检查配置**: 确保配置文件正确
3. **测试连接**: 使用测试功能验证网络

### 联系方式

- 📧 邮箱: <EMAIL>
- 📱 QQ群: 123456789
- 🌐 官网: https://example.com

### 更新说明

定期检查更新:
1. 下载最新版本
2. 备份现有配置
3. 替换程序文件
4. 恢复配置文件

---

**提示**: 本工具仅供学习和合法用途使用，请遵守相关法律法规和网站使用条款。
