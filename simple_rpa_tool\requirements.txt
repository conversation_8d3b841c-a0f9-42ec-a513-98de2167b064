# 小白专用RPA工具依赖包
# 适用于Python *********版本

# 核心依赖
selenium==4.18.1
webdriver-manager==4.0.1
requests==2.31.0
pandas==2.2.1
openpyxl==3.1.2

# GUI界面
tkinter-tooltip==2.1.0
pillow==10.2.0
customtkinter==5.2.2

# 数据处理
numpy==1.26.4
lxml==5.1.0
beautifulsoup4==4.12.3

# 配置和环境
PyYAML==6.0.1
python-dotenv==1.0.1

# 网络和安全
urllib3==2.2.1
certifi==2024.2.2

# 定时任务
schedule==1.2.1
APScheduler==3.10.4

# 日志处理
colorlog==6.8.2

# 系统集成
pywin32==306
psutil==5.9.8

# 打包工具
pyinstaller==6.5.0

# 文件处理
pathlib2==2.3.7

# 可选依赖 - 高级功能
# opencv-python==********  # 图像识别
# pytesseract==0.3.10      # OCR文字识别
# keyboard==0.13.5         # 键盘监听
# mouse==0.7.1             # 鼠标控制

# 可选依赖 - 聊天软件集成
# wechatpy==1.8.18         # 微信开发
# requests-toolbelt==1.0.0 # 文件上传

# 可选依赖 - 数据库支持
# sqlalchemy==2.0.29       # 数据库ORM
# sqlite3                  # SQLite (Python内置)

# 开发工具（仅开发时需要）
# pytest==8.1.1
# black==24.3.0
# flake8==7.0.0
