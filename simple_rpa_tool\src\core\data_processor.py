#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
提供数据清洗、转换和保存功能
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
import pandas as pd


class DataProcessor:
    """数据处理类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.logger = logging.getLogger(__name__)
    
    def clean_data(self, data):
        """清洗数据"""
        try:
            if not data:
                return []
            
            cleaned_data = []
            
            for item in data:
                if isinstance(item, dict):
                    # 清理字典数据
                    cleaned_item = {}
                    for key, value in item.items():
                        # 清理键名
                        clean_key = str(key).strip()
                        if not clean_key:
                            clean_key = "未命名列"
                        
                        # 清理值
                        if value is None:
                            clean_value = ""
                        else:
                            clean_value = str(value).strip()
                        
                        cleaned_item[clean_key] = clean_value
                    
                    # 只保留非空记录
                    if any(v for v in cleaned_item.values() if v):
                        cleaned_data.append(cleaned_item)
                
                elif isinstance(item, (list, tuple)):
                    # 处理列表或元组数据
                    cleaned_item = [str(v).strip() if v is not None else "" for v in item]
                    if any(v for v in cleaned_item if v):
                        cleaned_data.append(cleaned_item)
                
                else:
                    # 处理其他类型数据
                    clean_value = str(item).strip()
                    if clean_value:
                        cleaned_data.append({"内容": clean_value})
            
            self.logger.info(f"数据清洗完成，从 {len(data)} 条记录清洗为 {len(cleaned_data)} 条")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {e}")
            return data
    
    def convert_to_dataframe(self, data):
        """转换为DataFrame"""
        try:
            if not data:
                return pd.DataFrame()
            
            # 如果数据是字典列表
            if isinstance(data[0], dict):
                df = pd.DataFrame(data)
            
            # 如果数据是列表的列表
            elif isinstance(data[0], (list, tuple)):
                df = pd.DataFrame(data)
            
            # 其他情况
            else:
                df = pd.DataFrame({"数据": data})
            
            # 添加序号列
            if not df.empty and "序号" not in df.columns:
                df.insert(0, "序号", range(1, len(df) + 1))
            
            # 添加提取时间列
            if not df.empty and "提取时间" not in df.columns:
                df["提取时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            self.logger.info(f"DataFrame创建成功，形状: {df.shape}")
            return df
            
        except Exception as e:
            self.logger.error(f"DataFrame转换失败: {e}")
            return pd.DataFrame()
    
    def save_data(self, data, output_dir="output", file_format="Excel (.xlsx)"):
        """保存数据到文件"""
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # 清洗数据
            cleaned_data = self.clean_data(data)
            
            if not cleaned_data:
                self.logger.warning("没有数据需要保存")
                return None
            
            # 转换为DataFrame
            df = self.convert_to_dataframe(cleaned_data)
            
            if df.empty:
                self.logger.warning("DataFrame为空，无法保存")
                return None
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 根据格式保存文件
            if "Excel" in file_format or ".xlsx" in file_format:
                filename = f"rpa_data_{timestamp}.xlsx"
                filepath = output_path / filename
                df.to_excel(filepath, index=False, engine='openpyxl')
                
            elif "CSV" in file_format or ".csv" in file_format:
                filename = f"rpa_data_{timestamp}.csv"
                filepath = output_path / filename
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                
            elif "JSON" in file_format or ".json" in file_format:
                filename = f"rpa_data_{timestamp}.json"
                filepath = output_path / filename
                df.to_json(filepath, orient='records', ensure_ascii=False, indent=2)
                
            else:
                # 默认保存为Excel
                filename = f"rpa_data_{timestamp}.xlsx"
                filepath = output_path / filename
                df.to_excel(filepath, index=False, engine='openpyxl')
            
            self.logger.info(f"数据保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"数据保存失败: {e}")
            raise
    
    def save_multiple_formats(self, data, output_dir="output"):
        """保存为多种格式"""
        try:
            saved_files = []
            formats = ["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"]
            
            for fmt in formats:
                try:
                    filepath = self.save_data(data, output_dir, fmt)
                    if filepath:
                        saved_files.append(filepath)
                except Exception as e:
                    self.logger.error(f"保存 {fmt} 格式失败: {e}")
            
            return saved_files
            
        except Exception as e:
            self.logger.error(f"多格式保存失败: {e}")
            return []
    
    def validate_data(self, data):
        """验证数据质量"""
        try:
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'statistics': {}
            }
            
            if not data:
                validation_result['is_valid'] = False
                validation_result['errors'].append("数据为空")
                return validation_result
            
            # 统计信息
            validation_result['statistics'] = {
                'total_records': len(data),
                'data_type': type(data[0]).__name__ if data else 'unknown',
                'extraction_time': datetime.now().isoformat()
            }
            
            # 检查数据结构
            if isinstance(data[0], dict):
                # 字典数据检查
                all_keys = set()
                for item in data:
                    if isinstance(item, dict):
                        all_keys.update(item.keys())
                
                validation_result['statistics']['columns'] = list(all_keys)
                validation_result['statistics']['column_count'] = len(all_keys)
                
                # 检查空值
                empty_records = sum(1 for item in data if not any(v for v in item.values() if v))
                if empty_records > 0:
                    validation_result['warnings'].append(f"发现 {empty_records} 条空记录")
            
            # 数据量检查
            if len(data) < 1:
                validation_result['warnings'].append("数据量较少")
            elif len(data) > 10000:
                validation_result['warnings'].append("数据量较大，处理可能较慢")
            
            self.logger.info(f"数据验证完成: {validation_result['statistics']}")
            return validation_result
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return {
                'is_valid': False,
                'errors': [f"验证过程出错: {e}"],
                'warnings': [],
                'statistics': {}
            }
    
    def generate_summary(self, data):
        """生成数据摘要"""
        try:
            if not data:
                return "无数据"
            
            summary = []
            summary.append(f"数据总数: {len(data)} 条")
            
            if isinstance(data[0], dict):
                # 字典数据摘要
                all_keys = set()
                for item in data:
                    if isinstance(item, dict):
                        all_keys.update(item.keys())
                
                summary.append(f"字段数量: {len(all_keys)}")
                summary.append(f"字段名称: {', '.join(list(all_keys)[:5])}")
                if len(all_keys) > 5:
                    summary.append("...")
            
            summary.append(f"提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            return "\n".join(summary)
            
        except Exception as e:
            self.logger.error(f"摘要生成失败: {e}")
            return f"摘要生成失败: {e}"
    
    def export_config(self, config, output_dir="config"):
        """导出配置文件"""
        try:
            config_path = Path(output_dir)
            config_path.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_file = config_path / f"rpa_config_{timestamp}.json"
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置导出成功: {config_file}")
            return str(config_file)
            
        except Exception as e:
            self.logger.error(f"配置导出失败: {e}")
            return None
