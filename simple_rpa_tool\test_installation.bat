@echo off
chcp 65001 >nul
echo ========================================
echo 小白专用RPA工具 - 安装测试脚本
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo.

REM 测试1: 检查Python版本
echo 🔍 测试1: 检查Python版本
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请运行 setup_python311.bat 安装Python
    goto :test_failed
) else (
    echo ✅ Python已安装
)

REM 测试2: 检查虚拟环境
echo.
echo 🔍 测试2: 检查虚拟环境
if exist "venv_rpa311" (
    echo ✅ 虚拟环境存在
) else (
    echo ❌ 虚拟环境不存在
    echo 请运行 install_and_run.bat 创建虚拟环境
    goto :test_failed
)

REM 测试3: 激活虚拟环境并检查依赖
echo.
echo 🔍 测试3: 检查虚拟环境依赖
call venv_rpa311\Scripts\activate.bat

echo 当前Python路径:
where python

echo.
echo 检查关键依赖包:
python -c "import selenium; print('✅ selenium:', selenium.__version__)" 2>nul || echo "❌ selenium 未安装"
python -c "import pandas; print('✅ pandas:', pandas.__version__)" 2>nul || echo "❌ pandas 未安装"
python -c "import openpyxl; print('✅ openpyxl:', openpyxl.__version__)" 2>nul || echo "❌ openpyxl 未安装"
python -c "import requests; print('✅ requests:', requests.__version__)" 2>nul || echo "❌ requests 未安装"
python -c "import tkinter; print('✅ tkinter: 可用')" 2>nul || echo "❌ tkinter 未安装"

REM 测试4: 检查项目文件结构
echo.
echo 🔍 测试4: 检查项目文件结构
set "missing_files="

if exist "main_gui.py" (
    echo ✅ main_gui.py
) else (
    echo ❌ main_gui.py
    set "missing_files=1"
)

if exist "main_cli.py" (
    echo ✅ main_cli.py
) else (
    echo ❌ main_cli.py
    set "missing_files=1"
)

if exist "requirements.txt" (
    echo ✅ requirements.txt
) else (
    echo ❌ requirements.txt
    set "missing_files=1"
)

if exist "src\core\web_automation.py" (
    echo ✅ src\core\web_automation.py
) else (
    echo ❌ src\core\web_automation.py
    set "missing_files=1"
)

if exist "src\core\data_processor.py" (
    echo ✅ src\core\data_processor.py
) else (
    echo ❌ src\core\data_processor.py
    set "missing_files=1"
)

if exist "config\default_config.json" (
    echo ✅ config\default_config.json
) else (
    echo ❌ config\default_config.json
    set "missing_files=1"
)

if defined missing_files (
    echo.
    echo ❌ 部分项目文件缺失
    goto :test_failed
)

REM 测试5: 测试GUI启动
echo.
echo 🔍 测试5: 测试GUI模块导入
python -c "
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
try:
    from src.core.web_automation import WebAutomation
    from src.core.data_processor import DataProcessor
    from src.utils.logger import setup_logger
    print('✅ 核心模块导入成功')
except ImportError as e:
    print(f'❌ 模块导入失败: {e}')
    sys.exit(1)
"

if %errorlevel% neq 0 (
    echo ❌ 核心模块导入失败
    goto :test_failed
)

REM 测试6: 创建测试目录
echo.
echo 🔍 测试6: 创建必要目录
for %%d in (config logs output templates) do (
    if not exist "%%d" (
        mkdir "%%d"
        echo ✅ 创建目录: %%d
    ) else (
        echo ✅ 目录已存在: %%d
    )
)

REM 测试7: 测试配置文件
echo.
echo 🔍 测试7: 测试配置文件读取
python -c "
import json
import os
try:
    with open('config/default_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    print('✅ 配置文件读取成功')
    print(f'应用名称: {config.get(\"app\", {}).get(\"name\", \"未知\")}')
except Exception as e:
    print(f'❌ 配置文件读取失败: {e}')
    exit(1)
"

if %errorlevel% neq 0 (
    echo ❌ 配置文件测试失败
    goto :test_failed
)

REM 测试8: 测试Chrome浏览器
echo.
echo 🔍 测试8: 检查Chrome浏览器
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Chrome浏览器已安装
) else (
    echo ⚠️ Chrome浏览器未找到，可能影响网页自动化功能
    echo 建议安装Chrome浏览器以获得最佳体验
)

REM 所有测试通过
echo.
echo ========================================
echo 🎉 所有测试通过！
echo ========================================
echo.
echo 📋 测试结果摘要:
echo ✅ Python环境正常
echo ✅ 虚拟环境配置正确
echo ✅ 依赖包安装完整
echo ✅ 项目文件结构完整
echo ✅ 核心模块可正常导入
echo ✅ 配置文件格式正确
echo.
echo 🚀 可以开始使用RPA工具了！
echo.
echo 💡 下一步操作:
echo 1. 双击运行 main_gui.py 启动GUI界面
echo 2. 或运行 python main_cli.py --help 查看命令行选项
echo 3. 或运行 build_exe.bat 打包为exe文件
echo.
goto :end

:test_failed
echo.
echo ========================================
echo ❌ 测试失败
echo ========================================
echo.
echo 🔧 建议解决步骤:
echo 1. 检查Python是否正确安装
echo 2. 重新运行 install_and_run.bat
echo 3. 确保网络连接正常
echo 4. 查看上面的具体错误信息
echo.

:end
echo 按任意键退出...
pause >nul

REM 停用虚拟环境
if defined VIRTUAL_ENV (
    deactivate
)
