@echo off
chcp 65001 >nul
echo ========================================
echo 小白专用RPA工具 - 一键安装运行脚本
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 显示当前目录
echo 📁 当前目录: %CD%
echo.

REM 检查Python版本
echo 🔍 检查Python版本...
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 解决方法:
    echo 1. 运行 setup_python311.bat 安装Python 3.11.6
    echo 2. 或手动安装Python并添加到PATH环境变量
    echo.
    pause
    exit /b 1
)

REM 检查是否为Python 3.11.6
python --version | findstr "3.11.6" >nul
if %errorlevel% neq 0 (
    echo ⚠️ 建议使用Python 3.11.6版本以确保最佳兼容性
    echo 当前版本: 
    python --version
    echo.
    echo 是否继续? (Y/N)
    set /p choice=
    if /i not "%choice%"=="Y" (
        echo 安装已取消
        pause
        exit /b 1
    )
)

echo ✅ Python版本检查通过

REM 检查pip
echo.
echo 🔍 检查pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip未找到
    echo 请重新安装Python并确保包含pip
    pause
    exit /b 1
)
echo ✅ pip可用

REM 创建虚拟环境
echo.
echo 🔧 创建Python 3.11.6虚拟环境...
if exist "venv_rpa311" (
    echo ✅ 虚拟环境已存在
) else (
    echo 正在创建虚拟环境 venv_rpa311...
    python -m venv venv_rpa311
    if %errorlevel% neq 0 (
        echo ❌ 虚拟环境创建失败
        echo 可能原因: 权限不足或磁盘空间不够
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

REM 激活虚拟环境
echo.
echo 🔧 激活虚拟环境...
if exist "venv_rpa311\Scripts\activate.bat" (
    call venv_rpa311\Scripts\activate.bat
    echo ✅ 虚拟环境激活成功
    echo 当前Python路径: 
    where python
) else (
    echo ❌ 虚拟环境激活脚本不存在
    pause
    exit /b 1
)

REM 升级pip
echo.
echo 🔧 升级pip到最新版本...
python -m pip install --upgrade pip

REM 安装依赖包
echo.
echo 📦 安装依赖包...
if not exist "venv_rpa311\installed.flag" (
    echo 正在安装依赖包，这可能需要几分钟...
    echo.
    
    REM 使用国内镜像源加速下载
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    
    if %errorlevel% equ 0 (
        echo. > venv_rpa311\installed.flag
        echo ✅ 依赖包安装完成
    ) else (
        echo ❌ 依赖包安装失败
        echo.
        echo 尝试使用默认源重新安装...
        pip install -r requirements.txt
        if %errorlevel% equ 0 (
            echo. > venv_rpa311\installed.flag
            echo ✅ 依赖包安装完成
        ) else (
            echo ❌ 依赖包安装失败
            echo 请检查网络连接或手动安装
            pause
            exit /b 1
        )
    )
) else (
    echo ✅ 依赖包已安装
)

REM 创建必要的目录
echo.
echo 📁 创建项目目录...
if not exist "config" mkdir config
if not exist "logs" mkdir logs
if not exist "output" mkdir output
if not exist "templates" mkdir templates
echo ✅ 目录创建完成

REM 检查主程序文件
echo.
echo 🔍 检查程序文件...
if exist "main_gui.py" (
    echo ✅ GUI主程序存在
) else (
    echo ⚠️ main_gui.py 不存在，将在下一步创建
)

REM 显示虚拟环境信息
echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 📊 环境信息:
echo Python版本: 
python --version
echo.
echo Python路径: 
where python
echo.
echo 虚拟环境: venv_rpa311
echo 项目目录: %CD%
echo.

REM 询问是否启动GUI
echo 🚀 是否现在启动RPA工具GUI界面? (Y/N)
set /p start_choice=
if /i "%start_choice%"=="Y" (
    if exist "main_gui.py" (
        echo.
        echo 🚀 启动GUI界面...
        python main_gui.py
    ) else (
        echo.
        echo ⚠️ GUI程序文件不存在
        echo 请先创建 main_gui.py 文件
    )
) else (
    echo.
    echo 💡 手动启动方法:
    echo 1. 激活虚拟环境: venv_rpa311\Scripts\activate
    echo 2. 运行GUI程序: python main_gui.py
    echo 3. 或运行命令行版本: python main_cli.py
)

echo.
echo 📚 更多帮助请查看 README.md 文件
echo.
pause

REM 停用虚拟环境
if defined VIRTUAL_ENV (
    deactivate
)
