#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小白专用RPA工具 - GUI主程序
简单易用的图形界面，无需编程知识
"""

import os
import sys
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.gui.main_window import MainWindow
    from src.core.web_automation import WebAutomation
    from src.core.data_processor import DataProcessor
    from src.utils.logger import setup_logger
except ImportError as e:
    # 如果模块不存在，创建基础GUI
    print(f"模块导入失败: {e}")
    print("将创建基础GUI界面")


class SimpleRPAGUI:
    """简单RPA工具GUI主类"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("小白专用RPA工具 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 设置图标（如果存在）
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass
        
        # 初始化变量
        self.config = {}
        self.is_running = False
        self.automation = None
        
        # 创建界面
        self.create_widgets()
        self.load_config()
        
        # 设置日志
        self.setup_logging()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🤖 小白专用RPA工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 创建选项卡
        notebook = ttk.Notebook(main_frame)
        notebook.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        main_frame.rowconfigure(1, weight=1)
        
        # 基本配置选项卡
        self.create_config_tab(notebook)
        
        # 任务执行选项卡
        self.create_task_tab(notebook)
        
        # 定时任务选项卡
        self.create_schedule_tab(notebook)
        
        # 日志选项卡
        self.create_log_tab(notebook)
        
        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Button(button_frame, text="💾 保存配置", 
                  command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="📁 打开输出目录", 
                  command=self.open_output_dir).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="📖 帮助文档", 
                  command=self.show_help).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ 退出", 
                  command=self.quit_app).pack(side=tk.RIGHT)
    
    def create_config_tab(self, notebook):
        """创建配置选项卡"""
        config_frame = ttk.Frame(notebook, padding="10")
        notebook.add(config_frame, text="⚙️ 基本配置")
        
        # 网站配置
        website_group = ttk.LabelFrame(config_frame, text="网站配置", padding="10")
        website_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(website_group, text="目标网站URL:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.url_var = tk.StringVar(value="https://example.com")
        ttk.Entry(website_group, textvariable=self.url_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        ttk.Label(website_group, text="用户名:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.username_var = tk.StringVar()
        ttk.Entry(website_group, textvariable=self.username_var, width=30).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        ttk.Label(website_group, text="密码:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.password_var = tk.StringVar()
        ttk.Entry(website_group, textvariable=self.password_var, show="*", width=30).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        website_group.columnconfigure(1, weight=1)
        
        # 浏览器配置
        browser_group = ttk.LabelFrame(config_frame, text="浏览器配置", padding="10")
        browser_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(browser_group, text="浏览器类型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.browser_var = tk.StringVar(value="Chrome")
        browser_combo = ttk.Combobox(browser_group, textvariable=self.browser_var, 
                                   values=["Chrome", "Firefox", "Edge"], state="readonly")
        browser_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        self.headless_var = tk.BooleanVar()
        ttk.Checkbutton(browser_group, text="后台运行（不显示浏览器窗口）", 
                       variable=self.headless_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)
        
        # 输出配置
        output_group = ttk.LabelFrame(config_frame, text="输出配置", padding="10")
        output_group.pack(fill=tk.X)
        
        ttk.Label(output_group, text="输出目录:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.output_dir_var = tk.StringVar(value="output")
        ttk.Entry(output_group, textvariable=self.output_dir_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        ttk.Button(output_group, text="浏览", command=self.browse_output_dir).grid(row=0, column=2, padx=(10, 0), pady=2)
        
        ttk.Label(output_group, text="文件格式:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.format_var = tk.StringVar(value="Excel (.xlsx)")
        format_combo = ttk.Combobox(output_group, textvariable=self.format_var,
                                  values=["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"], state="readonly")
        format_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        output_group.columnconfigure(1, weight=1)
    
    def create_task_tab(self, notebook):
        """创建任务执行选项卡"""
        task_frame = ttk.Frame(notebook, padding="10")
        notebook.add(task_frame, text="🚀 任务执行")
        
        # 任务控制
        control_group = ttk.LabelFrame(task_frame, text="任务控制", padding="10")
        control_group.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(control_group)
        button_frame.pack(fill=tk.X)
        
        self.start_button = ttk.Button(button_frame, text="▶️ 开始执行", 
                                     command=self.start_task, style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ 停止任务", 
                                    command=self.stop_task, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="🧪 测试连接", 
                  command=self.test_connection).pack(side=tk.LEFT)
        
        # 进度显示
        progress_group = ttk.LabelFrame(task_frame, text="执行进度", padding="10")
        progress_group.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(progress_group, textvariable=self.progress_var).pack(anchor=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_group, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # 结果显示
        result_group = ttk.LabelFrame(task_frame, text="执行结果", padding="10")
        result_group.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(result_group)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = tk.Text(text_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_schedule_tab(self, notebook):
        """创建定时任务选项卡"""
        schedule_frame = ttk.Frame(notebook, padding="10")
        notebook.add(schedule_frame, text="⏰ 定时任务")
        
        # 定时设置
        time_group = ttk.LabelFrame(schedule_frame, text="定时设置", padding="10")
        time_group.pack(fill=tk.X, pady=(0, 10))
        
        self.schedule_enabled_var = tk.BooleanVar()
        ttk.Checkbutton(time_group, text="启用定时任务", 
                       variable=self.schedule_enabled_var).pack(anchor=tk.W, pady=(0, 10))
        
        time_frame = ttk.Frame(time_group)
        time_frame.pack(fill=tk.X)
        
        ttk.Label(time_frame, text="执行时间:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.hour_var = tk.StringVar(value="09")
        self.minute_var = tk.StringVar(value="00")
        
        hour_spin = ttk.Spinbox(time_frame, from_=0, to=23, textvariable=self.hour_var, width=5, format="%02.0f")
        hour_spin.grid(row=0, column=1, padx=(10, 5), pady=2)
        ttk.Label(time_frame, text=":").grid(row=0, column=2, pady=2)
        minute_spin = ttk.Spinbox(time_frame, from_=0, to=59, textvariable=self.minute_var, width=5, format="%02.0f")
        minute_spin.grid(row=0, column=3, padx=(5, 0), pady=2)
        
        # 执行频率
        freq_frame = ttk.Frame(time_group)
        freq_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(freq_frame, text="执行频率:").pack(anchor=tk.W)
        
        self.freq_var = tk.StringVar(value="daily")
        ttk.Radiobutton(freq_frame, text="每天", variable=self.freq_var, value="daily").pack(anchor=tk.W)
        ttk.Radiobutton(freq_frame, text="工作日", variable=self.freq_var, value="weekdays").pack(anchor=tk.W)
        ttk.Radiobutton(freq_frame, text="自定义", variable=self.freq_var, value="custom").pack(anchor=tk.W)
        
        # 任务状态
        status_group = ttk.LabelFrame(schedule_frame, text="任务状态", padding="10")
        status_group.pack(fill=tk.X)
        
        self.schedule_status_var = tk.StringVar(value="未启动")
        ttk.Label(status_group, textvariable=self.schedule_status_var).pack(anchor=tk.W)
        
        ttk.Button(status_group, text="启动定时任务", 
                  command=self.start_scheduler).pack(anchor=tk.W, pady=(10, 0))
    
    def create_log_tab(self, notebook):
        """创建日志选项卡"""
        log_frame = ttk.Frame(notebook, padding="10")
        notebook.add(log_frame, text="📋 日志")
        
        # 日志控制
        log_control = ttk.Frame(log_frame)
        log_control.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(log_control, text="🔄 刷新日志", 
                  command=self.refresh_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control, text="🗑️ 清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_control, text="💾 保存日志", 
                  command=self.save_log).pack(side=tk.LEFT)
        
        # 日志显示
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(log_text_frame, height=20, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_logging(self):
        """设置日志"""
        # 创建logs目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 初始化日志显示
        self.log_message("🚀 RPA工具启动")
        self.log_message(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 添加到GUI日志
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 同时添加到结果显示
        self.result_text.insert(tk.END, log_entry)
        self.result_text.see(tk.END)
        
        # 更新界面
        self.root.update_idletasks()
    
    def load_config(self):
        """加载配置"""
        config_file = Path("config/user_config.json")
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                    
                # 应用配置到界面
                self.url_var.set(self.config.get('url', ''))
                self.username_var.set(self.config.get('username', ''))
                self.password_var.set(self.config.get('password', ''))
                self.output_dir_var.set(self.config.get('output_dir', 'output'))
                self.headless_var.set(self.config.get('headless', False))
                
                self.log_message("✅ 配置加载成功")
            except Exception as e:
                self.log_message(f"❌ 配置加载失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            # 创建config目录
            config_dir = Path("config")
            config_dir.mkdir(exist_ok=True)
            
            # 收集配置
            self.config = {
                'url': self.url_var.get(),
                'username': self.username_var.get(),
                'password': self.password_var.get(),
                'output_dir': self.output_dir_var.get(),
                'browser': self.browser_var.get(),
                'headless': self.headless_var.get(),
                'format': self.format_var.get(),
                'schedule_enabled': self.schedule_enabled_var.get(),
                'schedule_time': f"{self.hour_var.get()}:{self.minute_var.get()}",
                'schedule_freq': self.freq_var.get()
            }
            
            # 保存到文件
            config_file = config_dir / "user_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            self.log_message("✅ 配置保存成功")
            messagebox.showinfo("成功", "配置已保存")
            
        except Exception as e:
            self.log_message(f"❌ 配置保存失败: {e}")
            messagebox.showerror("错误", f"配置保存失败: {e}")
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)
    
    def start_task(self):
        """开始执行任务"""
        if self.is_running:
            return
        
        # 检查配置
        if not self.url_var.get():
            messagebox.showerror("错误", "请先配置目标网站URL")
            return
        
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.progress_var.set("正在执行任务...")
        
        # 在新线程中执行任务
        thread = threading.Thread(target=self.run_task)
        thread.daemon = True
        thread.start()

    def run_task(self):
        """执行RPA任务"""
        try:
            self.log_message("🚀 开始执行RPA任务")
            self.log_message(f"🌐 目标网站: {self.url_var.get()}")

            # 尝试导入并使用实际的RPA功能
            try:
                from src.core.web_automation import WebAutomation
                from src.core.data_processor import DataProcessor

                # 创建自动化实例
                automation = WebAutomation({
                    'url': self.url_var.get(),
                    'username': self.username_var.get(),
                    'password': self.password_var.get(),
                    'browser': self.browser_var.get().lower(),
                    'headless': self.headless_var.get()
                })

                self.log_message("🔧 初始化浏览器...")
                automation.init_browser()

                self.log_message("🔑 正在登录...")
                automation.login()

                self.log_message("📊 正在提取数据...")
                data = automation.extract_data()

                self.log_message("💾 正在处理和保存数据...")
                processor = DataProcessor()
                result_file = processor.save_data(data, self.output_dir_var.get(), self.format_var.get())

                self.log_message(f"✅ 任务执行成功")
                self.log_message(f"📁 输出文件: {result_file}")

            except ImportError:
                # 如果模块不存在，使用模拟执行
                self.log_message("⚠️ 使用模拟模式执行（实际模块未找到）")
                import time

                self.log_message("🔧 初始化浏览器...")
                time.sleep(2)

                self.log_message("🔑 正在登录...")
                time.sleep(3)

                self.log_message("📊 正在提取数据...")
                time.sleep(5)

                self.log_message("💾 正在保存文件...")
                time.sleep(2)

                # 创建输出目录
                output_dir = Path(self.output_dir_var.get())
                output_dir.mkdir(exist_ok=True)

                # 创建示例输出文件
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = output_dir / f"rpa_result_{timestamp}.txt"

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(f"RPA任务执行结果\n")
                    f.write(f"执行时间: {datetime.now()}\n")
                    f.write(f"目标网站: {self.url_var.get()}\n")
                    f.write(f"用户名: {self.username_var.get()}\n")
                    f.write("任务状态: 成功完成（模拟模式）\n")

                self.log_message(f"✅ 模拟任务执行成功")
                self.log_message(f"📁 输出文件: {output_file}")

        except Exception as e:
            self.log_message(f"❌ 任务执行失败: {e}")

        finally:
            # 重置界面状态
            self.root.after(0, self.task_completed)
    
    def task_completed(self):
        """任务完成后的界面更新"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.progress_var.set("任务完成")
    
    def stop_task(self):
        """停止任务"""
        self.is_running = False
        self.log_message("⏹️ 任务已停止")
        self.task_completed()
    
    def test_connection(self):
        """测试连接"""
        if not self.url_var.get():
            messagebox.showerror("错误", "请先输入目标网站URL")
            return
        
        self.log_message("🧪 测试网站连接...")
        
        # 在新线程中测试连接
        thread = threading.Thread(target=self.do_test_connection)
        thread.daemon = True
        thread.start()
    
    def do_test_connection(self):
        """执行连接测试"""
        try:
            import requests
            response = requests.get(self.url_var.get(), timeout=10)
            if response.status_code == 200:
                self.log_message("✅ 网站连接正常")
            else:
                self.log_message(f"⚠️ 网站返回状态码: {response.status_code}")
        except Exception as e:
            self.log_message(f"❌ 连接测试失败: {e}")
    
    def start_scheduler(self):
        """启动定时任务"""
        if self.schedule_enabled_var.get():
            self.log_message("⏰ 定时任务已启动")
            self.schedule_status_var.set("运行中")
        else:
            self.log_message("⚠️ 请先启用定时任务")
    
    def refresh_log(self):
        """刷新日志"""
        self.log_message("🔄 日志已刷新")
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.result_text.delete(1.0, tk.END)
        self.log_message("🗑️ 日志已清空")
    
    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get(1.0, tk.END)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = Path("logs") / f"rpa_log_{timestamp}.txt"
            
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(log_content)
            
            self.log_message(f"💾 日志已保存: {log_file}")
            messagebox.showinfo("成功", f"日志已保存到: {log_file}")
            
        except Exception as e:
            self.log_message(f"❌ 日志保存失败: {e}")
            messagebox.showerror("错误", f"日志保存失败: {e}")
    
    def open_output_dir(self):
        """打开输出目录"""
        output_dir = Path(self.output_dir_var.get())
        output_dir.mkdir(exist_ok=True)
        
        try:
            os.startfile(str(output_dir))
        except Exception as e:
            self.log_message(f"❌ 无法打开目录: {e}")
    
    def show_help(self):
        """显示帮助"""
        help_text = """
🤖 小白专用RPA工具使用帮助

📋 基本步骤:
1. 在"基本配置"中填写目标网站信息
2. 配置浏览器和输出设置
3. 点击"保存配置"
4. 在"任务执行"中点击"开始执行"
5. 查看执行结果和输出文件

⚙️ 配置说明:
- 目标网站URL: 要自动化的网站地址
- 用户名/密码: 网站登录凭据
- 浏览器类型: 推荐使用Chrome
- 后台运行: 勾选后浏览器不会显示窗口
- 输出目录: 结果文件保存位置

⏰ 定时任务:
- 启用定时任务后可以自动执行
- 设置执行时间和频率
- 支持每天、工作日或自定义执行

📞 技术支持:
- 查看README.md文件获取详细说明
- 检查logs目录下的日志文件
- 确保Python 3.11.6环境正确配置
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        
        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)
    
    def quit_app(self):
        """退出应用"""
        if self.is_running:
            if messagebox.askyesno("确认", "任务正在执行中，确定要退出吗？"):
                self.stop_task()
                self.root.quit()
        else:
            self.root.quit()
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.quit_app()


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            messagebox.showerror("版本错误", "需要Python 3.8或更高版本")
            return
        
        # 创建必要的目录
        for directory in ["config", "logs", "output", "templates"]:
            Path(directory).mkdir(exist_ok=True)
        
        # 启动GUI
        app = SimpleRPAGUI()
        app.run()
        
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {e}")
        print(f"启动错误: {e}")


if __name__ == "__main__":
    main()
