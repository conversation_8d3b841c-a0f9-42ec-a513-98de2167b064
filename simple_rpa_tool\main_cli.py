#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小白专用RPA工具 - 命令行版本
提供命令行接口，支持批处理和自动化执行
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.core.web_automation import WebAutomation
    from src.core.data_processor import DataProcessor
    from src.utils.logger import setup_logger
except ImportError as e:
    print(f"模块导入失败: {e}")
    print("请确保已正确安装所有依赖包")
    sys.exit(1)


class RPACommandLine:
    """RPA命令行工具类"""
    
    def __init__(self):
        """初始化命令行工具"""
        self.logger = setup_logger("RPA_CLI")
        self.config = {}
        self.load_config()
    
    def load_config(self, config_file=None):
        """加载配置文件"""
        try:
            # 默认配置文件路径
            if not config_file:
                config_file = Path("config/user_config.json")
                if not config_file.exists():
                    config_file = Path("config/default_config.json")
            
            if Path(config_file).exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.logger.info(f"配置加载成功: {config_file}")
            else:
                self.logger.warning(f"配置文件不存在: {config_file}")
                self.config = self.get_default_config()
                
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            self.config = self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "url": "",
            "username": "",
            "password": "",
            "browser": "chrome",
            "headless": True,
            "output_dir": "output",
            "format": "Excel (.xlsx)"
        }
    
    def run_task(self, config_override=None):
        """执行RPA任务"""
        try:
            # 合并配置
            task_config = self.config.copy()
            if config_override:
                task_config.update(config_override)
            
            # 验证必要配置
            if not task_config.get('url'):
                raise ValueError("缺少目标URL配置")
            
            self.logger.info("开始执行RPA任务")
            self.logger.info(f"目标URL: {task_config['url']}")
            
            # 创建输出目录
            output_dir = Path(task_config.get('output_dir', 'output'))
            output_dir.mkdir(exist_ok=True)
            
            # 初始化组件
            automation = WebAutomation(task_config)
            processor = DataProcessor()
            
            # 执行自动化流程
            with automation:
                # 初始化浏览器
                automation.init_browser()
                
                # 登录（如果需要）
                if task_config.get('username') and task_config.get('password'):
                    automation.login()
                
                # 提取数据
                data = automation.extract_data()
                
                # 验证数据
                validation = processor.validate_data(data)
                if not validation['is_valid']:
                    raise ValueError(f"数据验证失败: {validation['errors']}")
                
                # 保存数据
                output_file = processor.save_data(
                    data, 
                    str(output_dir), 
                    task_config.get('format', 'Excel (.xlsx)')
                )
                
                # 生成摘要
                summary = processor.generate_summary(data)
                
                self.logger.info("任务执行成功")
                self.logger.info(f"输出文件: {output_file}")
                self.logger.info(f"数据摘要: {summary}")
                
                return {
                    'success': True,
                    'output_file': output_file,
                    'data_count': len(data),
                    'summary': summary
                }
                
        except Exception as e:
            self.logger.error(f"任务执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_connection(self, url=None):
        """测试网站连接"""
        try:
            test_url = url or self.config.get('url')
            if not test_url:
                raise ValueError("缺少测试URL")
            
            self.logger.info(f"测试连接: {test_url}")
            
            import requests
            response = requests.get(test_url, timeout=10)
            
            if response.status_code == 200:
                self.logger.info("连接测试成功")
                return True
            else:
                self.logger.warning(f"连接测试失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"连接测试失败: {e}")
            return False
    
    def validate_config(self, config_file=None):
        """验证配置文件"""
        try:
            if config_file:
                self.load_config(config_file)
            
            errors = []
            warnings = []
            
            # 检查必要字段
            required_fields = ['url']
            for field in required_fields:
                if not self.config.get(field):
                    errors.append(f"缺少必要字段: {field}")
            
            # 检查可选字段
            if not self.config.get('username'):
                warnings.append("未配置用户名，可能无法登录")
            
            if not self.config.get('password'):
                warnings.append("未配置密码，可能无法登录")
            
            # 检查输出目录
            output_dir = Path(self.config.get('output_dir', 'output'))
            if not output_dir.exists():
                try:
                    output_dir.mkdir(exist_ok=True)
                    warnings.append(f"输出目录不存在，已自动创建: {output_dir}")
                except Exception as e:
                    errors.append(f"无法创建输出目录: {e}")
            
            # 输出验证结果
            if errors:
                self.logger.error("配置验证失败:")
                for error in errors:
                    self.logger.error(f"  - {error}")
                return False
            
            if warnings:
                self.logger.warning("配置验证警告:")
                for warning in warnings:
                    self.logger.warning(f"  - {warning}")
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def create_sample_config(self, output_file="config/sample_config.json"):
        """创建示例配置文件"""
        try:
            sample_config = {
                "url": "https://example.com/login",
                "username": "your_username",
                "password": "your_password",
                "browser": "chrome",
                "headless": False,
                "output_dir": "output",
                "format": "Excel (.xlsx)",
                "schedule": {
                    "enabled": False,
                    "time": "09:00",
                    "frequency": "daily"
                }
            }
            
            # 创建目录
            output_path = Path(output_file)
            output_path.parent.mkdir(exist_ok=True)
            
            # 保存配置
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(sample_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"示例配置文件已创建: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"创建示例配置失败: {e}")
            return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="小白专用RPA工具 - 命令行版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main_cli.py --run                    # 使用默认配置执行任务
  python main_cli.py --run --config task.json # 使用指定配置执行任务
  python main_cli.py --test                   # 测试网站连接
  python main_cli.py --validate               # 验证配置文件
  python main_cli.py --create-config          # 创建示例配置文件
        """
    )
    
    parser.add_argument('--run', action='store_true', help='执行RPA任务')
    parser.add_argument('--test', action='store_true', help='测试网站连接')
    parser.add_argument('--validate', action='store_true', help='验证配置文件')
    parser.add_argument('--create-config', action='store_true', help='创建示例配置文件')
    parser.add_argument('--config', type=str, help='指定配置文件路径')
    parser.add_argument('--url', type=str, help='指定目标URL（覆盖配置文件）')
    parser.add_argument('--username', type=str, help='指定用户名（覆盖配置文件）')
    parser.add_argument('--password', type=str, help='指定密码（覆盖配置文件）')
    parser.add_argument('--output', type=str, help='指定输出目录（覆盖配置文件）')
    parser.add_argument('--headless', action='store_true', help='启用无头模式')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建RPA工具实例
    rpa = RPACommandLine()
    
    # 加载指定配置文件
    if args.config:
        rpa.load_config(args.config)
    
    # 执行相应操作
    if args.create_config:
        rpa.create_sample_config()
    
    elif args.validate:
        if rpa.validate_config(args.config):
            print("✅ 配置验证通过")
            sys.exit(0)
        else:
            print("❌ 配置验证失败")
            sys.exit(1)
    
    elif args.test:
        if rpa.test_connection(args.url):
            print("✅ 连接测试成功")
            sys.exit(0)
        else:
            print("❌ 连接测试失败")
            sys.exit(1)
    
    elif args.run:
        # 准备配置覆盖
        config_override = {}
        if args.url:
            config_override['url'] = args.url
        if args.username:
            config_override['username'] = args.username
        if args.password:
            config_override['password'] = args.password
        if args.output:
            config_override['output_dir'] = args.output
        if args.headless:
            config_override['headless'] = True
        
        # 执行任务
        result = rpa.run_task(config_override)
        
        if result['success']:
            print("✅ 任务执行成功")
            print(f"📁 输出文件: {result['output_file']}")
            print(f"📊 数据条数: {result['data_count']}")
            sys.exit(0)
        else:
            print("❌ 任务执行失败")
            print(f"错误信息: {result['error']}")
            sys.exit(1)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
