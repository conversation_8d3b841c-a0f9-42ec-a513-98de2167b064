# 小白专用RPA工具

一款专为初学者设计的RPA（机器人流程自动化）工具，具有简单易用的图形界面。

## 功能特性

- 🖱️ **简单易用的GUI界面** - 无需编程知识
- 🌐 **网页自动化** - 自动登录、数据提取
- 📊 **数据处理** - 自动整理和导出Excel文件
- ⏰ **定时任务** - 设置自动执行时间
- 📱 **消息推送** - 支持微信、钉钉等聊天软件
- 🔧 **一键打包** - 生成独立运行的exe文件

## 系统要求

- Windows 10/11
- Python *********（推荐）
- 4GB以上内存
- 网络连接

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv_rpa_311

# 激活虚拟环境
venv_rpa_311\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 运行工具

```bash
# 启动GUI界面
python main_gui.py

# 或者使用命令行模式
python main.py
```

### 3. 打包为exe

```bash
# 运行打包脚本
build_exe.bat
```

## 目录结构

```
rpa_tool_for_beginners/
├── main_gui.py              # GUI主程序
├── main.py                  # 命令行主程序
├── requirements.txt         # 依赖包列表
├── build_exe.bat           # 打包脚本
├── config/                 # 配置文件
│   ├── config.yaml         # 主配置文件
│   └── gui_config.json     # GUI配置
├── src/                    # 源代码
│   ├── gui/               # GUI界面
│   ├── core/              # 核心功能
│   ├── automation/        # 自动化模块
│   └── utils/             # 工具函数
├── templates/             # 模板文件
├── docs/                  # 文档
└── dist/                  # 打包输出目录
```

## 使用说明

### GUI模式（推荐新手）

1. 双击运行 `main_gui.py` 或打包后的exe文件
2. 在界面中配置目标网站信息
3. 设置数据提取规则
4. 点击"开始执行"按钮
5. 查看执行结果和导出的文件

### 命令行模式

```bash
# 执行单次任务
python main.py run

# 启动定时任务
python main.py schedule

# 测试配置
python main.py test
```

## 配置说明

主要配置文件位于 `config/config.yaml`，包含：

- 目标网站URL和登录信息
- 数据提取规则
- 文件输出设置
- 聊天软件推送配置
- 定时任务设置

## 常见问题

### 1. Python版本问题
确保使用Python 3.11.6版本，其他版本可能导致兼容性问题。

### 2. 依赖安装失败
建议使用虚拟环境，避免与系统Python环境冲突。

### 3. exe文件无法运行
检查是否包含了所有必要的依赖文件和资源。

### 4. 网页自动化失败
确保Chrome浏览器已安装，webdriver会自动下载。

## 技术支持

如遇到问题，请查看：
1. `docs/troubleshooting.md` - 故障排除指南
2. `docs/user_guide.md` - 详细使用指南
3. `logs/` 目录下的日志文件

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础GUI界面
- 网页自动化功能
- Excel数据导出
- 定时任务支持

## 许可证

MIT License - 详见 LICENSE 文件
