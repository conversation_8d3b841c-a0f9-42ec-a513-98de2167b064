@echo off
chcp 65001 >nul
echo ========================================
echo Python 3.11.6.64 自动安装脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限来安装Python
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认

REM 检查是否已安装Python 3.11.6
echo.
echo 🔍 检查当前Python版本...
python --version 2>nul | findstr "3.11.6" >nul
if %errorlevel% equ 0 (
    echo ✅ Python 3.11.6 已安装
    echo 跳过安装步骤
    goto :end
)

echo ⚠️ 未找到Python 3.11.6，开始安装...

REM 创建临时目录
if not exist "temp" mkdir temp
cd temp

REM 下载Python 3.11.6.64
echo.
echo 📥 下载Python 3.11.6.64...
echo 下载地址: https://www.python.org/ftp/python/3.11.6/python-3.11.6-amd64.exe
echo.
echo 正在下载，请稍候...

REM 使用PowerShell下载
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.6/python-3.11.6-amd64.exe' -OutFile 'python-3.11.6-amd64.exe'}"

if not exist "python-3.11.6-amd64.exe" (
    echo ❌ 下载失败，请检查网络连接
    echo.
    echo 手动下载方法:
    echo 1. 访问 https://www.python.org/downloads/release/python-3116/
    echo 2. 下载 "Windows installer (64-bit)"
    echo 3. 运行安装程序，勾选 "Add Python to PATH"
    pause
    cd ..
    exit /b 1
)

echo ✅ 下载完成

REM 安装Python
echo.
echo 🔧 安装Python 3.11.6...
echo 安装选项:
echo - 添加到PATH环境变量
echo - 安装pip包管理器
echo - 安装所有用户
echo.

python-3.11.6-amd64.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

REM 等待安装完成
timeout /t 30 /nobreak >nul

REM 验证安装
echo.
echo 🔍 验证安装...
python --version 2>nul | findstr "3.11.6" >nul
if %errorlevel% equ 0 (
    echo ✅ Python 3.11.6 安装成功
) else (
    echo ❌ 安装可能失败，请手动验证
    echo 请重新打开命令提示符并运行: python --version
)

REM 清理临时文件
cd ..
rmdir /s /q temp

:end
echo.
echo ========================================
echo 安装完成
echo ========================================
echo.
echo 下一步:
echo 1. 重新打开命令提示符
echo 2. 运行 install_and_run.bat 安装RPA工具
echo.
pause
