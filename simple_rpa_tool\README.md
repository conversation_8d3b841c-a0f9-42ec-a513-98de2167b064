# 小白专用RPA工具 v1.0

一款专为初学者设计的RPA（机器人流程自动化）工具，具有简单易用的图形界面，无需编程知识即可使用。

## 🎯 主要功能

- 🖱️ **简单GUI界面** - 点击式操作，无需编程
- 🌐 **网页自动化** - 自动登录、点击、数据提取
- 📊 **数据处理** - 自动整理和导出Excel文件
- ⏰ **定时任务** - 设置自动执行时间
- 📱 **消息推送** - 任务完成后自动通知
- 🔧 **一键打包** - 生成独立运行的exe文件

## 💻 系统要求

- Windows 10/11 (64位)
- Python ********* (推荐版本)
- 4GB以上内存
- Chrome浏览器
- 网络连接

## 🚀 快速开始

### 方法一：使用一键安装脚本（推荐）

1. 下载项目到本地
2. 双击运行 `setup_python311.bat` - 自动安装Python 3.11.6
3. 双击运行 `install_and_run.bat` - 自动配置环境并启动

### 方法二：手动安装

```bash
# 1. 确保Python 3.11.6已安装
python --version

# 2. 创建虚拟环境
python -m venv venv_rpa311

# 3. 激活虚拟环境
venv_rpa311\Scripts\activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 启动GUI界面
python main_gui.py
```

## 📁 项目结构

```
simple_rpa_tool/
├── main_gui.py                 # GUI主程序
├── main_cli.py                 # 命令行版本
├── requirements.txt            # 依赖包列表
├── setup_python311.bat        # Python 3.11.6安装脚本
├── install_and_run.bat         # 一键安装运行脚本
├── build_exe.bat              # 打包脚本
├── config/                     # 配置文件
│   ├── default_config.json    # 默认配置
│   └── user_config.json       # 用户配置
├── src/                        # 源代码
│   ├── gui/                   # GUI界面模块
│   │   ├── main_window.py     # 主窗口
│   │   ├── config_window.py   # 配置窗口
│   │   └── task_window.py     # 任务窗口
│   ├── core/                  # 核心功能
│   │   ├── web_automation.py  # 网页自动化
│   │   ├── data_processor.py  # 数据处理
│   │   └── file_manager.py    # 文件管理
│   ├── utils/                 # 工具函数
│   │   ├── logger.py          # 日志工具
│   │   └── helpers.py         # 辅助函数
│   └── scheduler/             # 定时任务
│       └── task_scheduler.py  # 任务调度器
├── templates/                  # 模板文件
│   ├── web_templates/         # 网页模板
│   └── excel_templates/       # Excel模板
├── docs/                       # 文档
│   ├── user_guide.md          # 用户指南
│   ├── troubleshooting.md     # 故障排除
│   └── examples/              # 使用示例
├── logs/                       # 日志文件
├── output/                     # 输出文件
└── dist/                       # 打包输出
```

## 🎮 使用方法

### GUI模式（推荐新手）

1. **启动程序**
   - 双击 `main_gui.py` 或打包后的exe文件

2. **配置任务**
   - 点击"新建任务"按钮
   - 填写目标网站URL
   - 设置登录信息
   - 配置数据提取规则

3. **执行任务**
   - 点击"开始执行"按钮
   - 观察执行过程
   - 查看结果文件

4. **设置定时任务**
   - 点击"定时任务"标签
   - 设置执行时间
   - 启用自动执行

### 命令行模式

```bash
# 执行单次任务
python main_cli.py --task config/task1.json

# 启动定时任务
python main_cli.py --schedule

# 测试配置
python main_cli.py --test
```

## 🔧 配置说明

### 基本配置 (config/default_config.json)

```json
{
  "browser": {
    "type": "chrome",
    "headless": false,
    "window_size": [1920, 1080]
  },
  "timeouts": {
    "page_load": 30,
    "element_wait": 10
  },
  "output": {
    "format": "xlsx",
    "path": "output"
  }
}
```

### 任务配置示例

```json
{
  "name": "数据提取任务",
  "target_url": "https://example.com/login",
  "login": {
    "username_field": "#username",
    "password_field": "#password",
    "submit_button": "#login-btn"
  },
  "data_extraction": {
    "table_selector": ".data-table",
    "columns": ["姓名", "年龄", "部门"]
  },
  "schedule": {
    "enabled": true,
    "time": "09:00",
    "days": ["monday", "wednesday", "friday"]
  }
}
```

## 📦 打包为exe文件

```bash
# 运行打包脚本
build_exe.bat

# 或手动打包
pyinstaller --onefile --windowed --icon=icon.ico main_gui.py
```

打包后的exe文件位于 `dist/` 目录，可以在任何Windows电脑上运行，无需安装Python。

## ❓ 常见问题

### 1. Python版本问题
**问题**: 提示Python版本不兼容
**解决**: 运行 `setup_python311.bat` 安装正确版本

### 2. 依赖安装失败
**问题**: pip install 报错
**解决**: 使用国内镜像源
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. exe文件无法运行
**问题**: 双击exe没有反应
**解决**: 
- 检查杀毒软件是否拦截
- 以管理员身份运行
- 查看 `logs/` 目录下的错误日志

### 4. 网页自动化失败
**问题**: 无法打开网页或找不到元素
**解决**:
- 确保Chrome浏览器已安装
- 检查网络连接
- 更新webdriver

## 📞 技术支持

- 📖 详细文档: `docs/user_guide.md`
- 🔧 故障排除: `docs/troubleshooting.md`
- 📝 使用示例: `docs/examples/`
- 📋 日志文件: `logs/`

## 🔄 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎨 简洁的GUI界面
- 🤖 基础网页自动化功能
- 📊 Excel数据导出
- ⏰ 定时任务支持
- 📦 一键打包功能

## 📄 许可证

MIT License - 详见 LICENSE 文件

---

**注意**: 本工具仅供学习和合法用途使用，请遵守相关网站的使用条款和法律法规。
