@echo off
chcp 65001 >nul
echo ========================================
echo 🤖 小白专用RPA工具 - 快速启动
echo ========================================
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 检查虚拟环境
if not exist "venv_rpa311" (
    echo ❌ 虚拟环境不存在
    echo.
    echo 🔧 请先运行以下步骤:
    echo 1. 双击 setup_python311.bat 安装Python
    echo 2. 双击 install_and_run.bat 安装RPA工具
    echo.
    pause
    exit /b 1
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv_rpa311\Scripts\activate.bat

REM 检查主程序
if not exist "main_gui.py" (
    echo ❌ 主程序文件不存在
    pause
    exit /b 1
)

REM 启动GUI
echo 🚀 启动RPA工具GUI界面...
echo.
python main_gui.py

REM 程序结束
echo.
echo 👋 RPA工具已关闭
pause

REM 停用虚拟环境
if defined VIRTUAL_ENV (
    deactivate
)
