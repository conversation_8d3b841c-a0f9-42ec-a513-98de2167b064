#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
提供统一的日志配置和管理
"""

import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path


def setup_logger(name=None, log_level=logging.INFO, log_dir="logs"):
    """
    设置日志记录器
    
    Args:
        name (str): 日志记录器名称
        log_level (int): 日志级别
        log_dir (str): 日志目录
    
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志目录
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # 获取日志记录器
    logger = logging.getLogger(name or __name__)
    logger.setLevel(log_level)
    
    # 如果已经有处理器，直接返回
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    log_file = log_path / f"rpa_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def get_logger(name=None):
    """
    获取日志记录器
    
    Args:
        name (str): 日志记录器名称
    
    Returns:
        logging.Logger: 日志记录器
    """
    return logging.getLogger(name or __name__)


class LogCapture:
    """日志捕获类，用于在GUI中显示日志"""
    
    def __init__(self, text_widget=None):
        """
        初始化日志捕获
        
        Args:
            text_widget: tkinter Text组件，用于显示日志
        """
        self.text_widget = text_widget
        self.logs = []
    
    def write(self, message):
        """写入日志消息"""
        if message.strip():
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message.strip()}"
            
            # 保存到内存
            self.logs.append(log_entry)
            
            # 显示到GUI
            if self.text_widget:
                try:
                    self.text_widget.insert('end', log_entry + '\n')
                    self.text_widget.see('end')
                except:
                    pass
    
    def flush(self):
        """刷新缓冲区"""
        pass
    
    def get_logs(self):
        """获取所有日志"""
        return self.logs
    
    def clear_logs(self):
        """清空日志"""
        self.logs.clear()
        if self.text_widget:
            try:
                self.text_widget.delete(1.0, 'end')
            except:
                pass
