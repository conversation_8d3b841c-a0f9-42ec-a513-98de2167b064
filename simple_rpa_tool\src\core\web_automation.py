#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页自动化模块
提供简单易用的网页自动化功能
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class WebAutomation:
    """网页自动化类"""
    
    def __init__(self, config):
        """
        初始化网页自动化
        
        Args:
            config (dict): 配置字典，包含url, username, password等
        """
        self.config = config
        self.driver = None
        self.wait = None
        self.logger = logging.getLogger(__name__)
        
    def init_browser(self):
        """初始化浏览器"""
        try:
            # Chrome选项
            chrome_options = Options()
            
            # 基本选项
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--start-maximized')
            
            # 如果启用无头模式
            if self.config.get('headless', False):
                chrome_options.add_argument('--headless')
            
            # 禁用图片加载以提高速度
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # 创建WebDriver服务
            service = Service(ChromeDriverManager().install())
            
            # 初始化浏览器
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.implicitly_wait(10)
            
            # 创建等待对象
            self.wait = WebDriverWait(self.driver, 30)
            
            self.logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            raise
    
    def login(self):
        """登录网站"""
        try:
            url = self.config.get('url')
            username = self.config.get('username')
            password = self.config.get('password')
            
            if not all([url, username, password]):
                raise ValueError("缺少必要的登录信息")
            
            # 访问登录页面
            self.logger.info(f"访问登录页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 查找用户名输入框（尝试多种选择器）
            username_selectors = [
                'input[name="username"]',
                'input[name="user"]',
                'input[name="email"]',
                'input[type="text"]',
                '#username',
                '#user',
                '#email',
                '.username',
                '.user-input'
            ]
            
            username_element = None
            for selector in username_selectors:
                try:
                    username_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not username_element:
                raise NoSuchElementException("未找到用户名输入框")
            
            # 输入用户名
            username_element.clear()
            username_element.send_keys(username)
            self.logger.info("用户名输入完成")
            
            # 查找密码输入框
            password_selectors = [
                'input[name="password"]',
                'input[name="pwd"]',
                'input[type="password"]',
                '#password',
                '#pwd',
                '.password',
                '.pwd-input'
            ]
            
            password_element = None
            for selector in password_selectors:
                try:
                    password_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not password_element:
                raise NoSuchElementException("未找到密码输入框")
            
            # 输入密码
            password_element.clear()
            password_element.send_keys(password)
            self.logger.info("密码输入完成")
            
            # 查找登录按钮
            login_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("登录")',
                'button:contains("Login")',
                '.login-btn',
                '.submit-btn',
                '#login',
                '#submit'
            ]
            
            login_element = None
            for selector in login_selectors:
                try:
                    if ':contains(' in selector:
                        # 使用XPath查找包含文本的按钮
                        text = selector.split(':contains("')[1].split('")')[0]
                        login_element = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{text}')]")
                    else:
                        login_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not login_element:
                raise NoSuchElementException("未找到登录按钮")
            
            # 点击登录按钮
            login_element.click()
            self.logger.info("登录按钮点击完成")
            
            # 等待登录完成
            time.sleep(5)
            
            # 检查是否登录成功（可以根据页面特征判断）
            current_url = self.driver.current_url
            if 'login' not in current_url.lower() or 'dashboard' in current_url.lower():
                self.logger.info("登录成功")
                return True
            else:
                self.logger.warning("登录可能失败，请检查凭据")
                return False
                
        except Exception as e:
            self.logger.error(f"登录失败: {e}")
            raise
    
    def extract_data(self):
        """提取数据"""
        try:
            self.logger.info("开始提取数据")
            
            # 等待页面加载
            time.sleep(3)
            
            # 尝试查找数据表格
            table_selectors = [
                'table',
                '.data-table',
                '.table',
                '#data-table',
                '[role="table"]'
            ]
            
            table_element = None
            for selector in table_selectors:
                try:
                    table_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if table_element:
                # 提取表格数据
                data = self.extract_table_data(table_element)
            else:
                # 如果没有表格，提取页面文本
                data = self.extract_page_text()
            
            self.logger.info(f"数据提取完成，共获取 {len(data)} 条记录")
            return data
            
        except Exception as e:
            self.logger.error(f"数据提取失败: {e}")
            raise
    
    def extract_table_data(self, table_element):
        """提取表格数据"""
        data = []
        
        try:
            # 获取表头
            headers = []
            header_elements = table_element.find_elements(By.CSS_SELECTOR, 'th')
            if not header_elements:
                header_elements = table_element.find_elements(By.CSS_SELECTOR, 'tr:first-child td')
            
            for header in header_elements:
                headers.append(header.text.strip())
            
            if not headers:
                headers = [f"列{i+1}" for i in range(10)]  # 默认列名
            
            # 获取数据行
            row_elements = table_element.find_elements(By.CSS_SELECTOR, 'tbody tr')
            if not row_elements:
                row_elements = table_element.find_elements(By.CSS_SELECTOR, 'tr')[1:]  # 跳过表头
            
            for row in row_elements:
                cells = row.find_elements(By.CSS_SELECTOR, 'td')
                if cells:
                    row_data = {}
                    for i, cell in enumerate(cells):
                        if i < len(headers):
                            row_data[headers[i]] = cell.text.strip()
                    if row_data:
                        data.append(row_data)
            
        except Exception as e:
            self.logger.error(f"表格数据提取失败: {e}")
        
        return data
    
    def extract_page_text(self):
        """提取页面文本数据"""
        data = []
        
        try:
            # 获取页面主要内容
            content_selectors = [
                '.content',
                '.main',
                '#content',
                '#main',
                'main',
                'article',
                '.article'
            ]
            
            content_element = None
            for selector in content_selectors:
                try:
                    content_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not content_element:
                content_element = self.driver.find_element(By.TAG_NAME, 'body')
            
            # 提取文本内容
            text_content = content_element.text.strip()
            
            # 按行分割并过滤空行
            lines = [line.strip() for line in text_content.split('\n') if line.strip()]
            
            # 转换为字典格式
            for i, line in enumerate(lines[:100]):  # 限制最多100行
                data.append({
                    '序号': i + 1,
                    '内容': line,
                    '提取时间': time.strftime('%Y-%m-%d %H:%M:%S')
                })
        
        except Exception as e:
            self.logger.error(f"页面文本提取失败: {e}")
        
        return data
    
    def close(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("浏览器已关闭")
        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {e}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
